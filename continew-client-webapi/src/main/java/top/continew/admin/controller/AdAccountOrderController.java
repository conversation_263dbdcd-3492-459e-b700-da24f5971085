package top.continew.admin.controller;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.continew.admin.biz.model.req.AdAccountOrderAddReq;
import top.continew.admin.biz.model.req.AdAccountOrderUpdateNameReq;
import top.continew.admin.biz.model.req.BalanceReminderAmountThresholdReq;
import top.continew.admin.biz.service.AdAccountOrderService;
import top.continew.admin.biz.service.CustomerRequirementService;



@RestController
@RequestMapping("/api/order")
@RequiredArgsConstructor
public class AdAccountOrderController {

    private final CustomerRequirementService customerRequirementService;

    private final AdAccountOrderService adAccountOrderService;
    @PostMapping("/requirement/add")
    @Operation(summary = "新增下户需求",description = "新增下户需求")
    public Long add(@RequestBody @Validated AdAccountOrderAddReq req) {
        return customerRequirementService.customerApply(req);
    }

    @PostMapping("/updateAdAccountName")
    @Operation(summary = "修改广告户名称",description = "修改广告户名称")
    public void updateAdAccountName(@RequestBody @Validated AdAccountOrderUpdateNameReq req) {
         adAccountOrderService.updateAdAccountName(req);
    }

    @PostMapping("/balanceReminderAmountThreshold")
    @Operation(summary = "余额提醒金额阈值",description = "余额提醒金额阈值")
    public void balanceReminderAmountThreshold(@RequestBody @Validated BalanceReminderAmountThresholdReq req) {
         adAccountOrderService.updateBalanceReminderAmountThresholdById(req);
    }
}
