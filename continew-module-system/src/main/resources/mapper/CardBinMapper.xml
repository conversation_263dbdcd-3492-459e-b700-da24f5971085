<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.CardBinMapper">
    <sql id="base_sql">
        SELECT bcb.id,
               bcb.card_bin,
               bcb.name,
               bcb.platform,
               bcb.card_scheme,
               bcb.enable,
               bcb.create_time,
               -- 当前正常状态的开卡数量
               COALESCE(card_stats.active_card_count, 0)               as activeCardCount,
               -- 当前冻结状态的开卡数量
               COALESCE(card_stats.frozen_card_count, 0)               as frozenCardCount,
               -- 近期正常状态的开卡数量
               COALESCE(recent_card_stats.recent_active_card_count, 0) as recentActiveCardCount,
               -- 近期冻结状态的开卡数量
               COALESCE(recent_card_stats.recent_frozen_card_count, 0) as recentFrozenCardCount
        FROM biz_card_bin bcb
                 -- 关联卡片统计数据
                 LEFT JOIN (SELECT bin_id,
                                   SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_card_count,
                                   SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as frozen_card_count
                            FROM biz_card
                            WHERE bin_id IS NOT NULL
                            GROUP BY bin_id) card_stats ON bcb.id = card_stats.bin_id
                 -- 关联近期卡片统计数据
                 LEFT JOIN (SELECT bin_id,
                                   SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as recent_active_card_count,
                                   SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as recent_frozen_card_count
                            FROM biz_card
                            WHERE bin_id IS NOT NULL
                              AND open_time >= DATE_SUB(NOW(), INTERVAL #{query.day} DAY)
                            GROUP BY bin_id) recent_card_stats ON bcb.id = recent_card_stats.bin_id
        <where>
            <if test="query.cardBin != null and query.cardBin != ''">
                AND bcb.card_bin LIKE CONCAT('%', #{query.cardBin}, '%')
            </if>
            <if test="query.platform != null">
                AND bcb.platform = #{query.platform}
            </if>
            <if test="query.enable != null">
                AND bcb.enable = #{query.enable}
            </if>
        </where>
        ORDER BY bcb.create_time DESC
    </sql>
    <select id="customerPage" resultType="top.continew.admin.biz.model.resp.CardBinResp">
        <include refid="base_sql" />
    </select>
    <select id="selectTransAmount" resultType="top.continew.admin.biz.model.resp.CardBinResp">
        select
            COALESCE(-SUM(bct.trans_amount), 0) as totalTransactionAmount,
            COALESCE(-SUM(CASE
                WHEN bct.stat_time >= DATE_SUB(NOW(), INTERVAL #{day} DAY)
                THEN bct.trans_amount
                ELSE 0
            END), 0) as recentTransactionAmount
        from biz_card bc
                 left join biz_card_transaction bct on bc.card_number = bct.card_number
        where bc.bin_id = #{binId}
          and bct.trans_status != 4
    </select>
    <select id="customList" resultType="top.continew.admin.biz.model.resp.CardBinResp">
        <include refid="base_sql" />
    </select>
    <select id="selectAvailableCardCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM biz_card WHERE bin_id = #{id}
    </select>

</mapper>