<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.AdAccountOrderMapper">
    <sql id="listAdAccountOrder">
        select o.*,
               bbmc.name                                                                                 as channelName,
               a.account_status,
               a.ban_time,
               a.spend_cap,
               a.amount_spent,
               a.bm_id                                                                                   as ad_account_bm_id,
               a.timezone,
               c.name                                                                                    as customer_name,
               su.nickname                                                                               as handle_user_name,
               a.cost                                                                                    as account_cost,
               a.prepay_account_balance,
               a.bm_auth_time,
               concat_ws(',', COALESCE(a.browser_id, a.browser_no), a.bm1_browser, browser_bm.ops_browser,
                         browser_bm.reserve_browser,
                         browser_bm.reserve_browser_bak,
                         browser_bm.observe_browser)                                                     as browser_no,
               a.bm_item_type                                                                            as bm_type,
               (select
                    COALESCE(sum(IF(r.type = 3, r.amount, 0)), 0) - COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0)
                from biz_customer_balance_record r
                where r.platform_ad_id = o.ad_account_id
                  and r.customer_id = o.customer_id
                  and r.create_time >= o.pay_time
                  AND (o.status != 5 OR r.trans_time &lt;= o.recycle_time))                             as recharge_amount,
               (SELECT COALESCE(
                               CASE
                                   WHEN o.status IN (3, 5) THEN (SELECT -SUM(t.trans_amount)
                                                                 FROM biz_card_transaction t
                                                                 WHERE t.ad_account_id = o.ad_account_id
                                                                   AND t.trans_status != 4
                                                                   AND t.stat_time >= o.finish_time
                                                                   AND (o.status != 5 OR t.stat_time &lt;= o.recycle_time)) END,
                               0
                       ))                                                                                AS card_spent,
               (select GROUP_CONCAT(tag_id) from biz_tag_relation where relation_id = o.id and type = 2) as tags
        from biz_ad_account_order o
                 left join biz_ad_account a on a.platform_ad_id = o.ad_account_id
                 left join biz_customer c on c.id = o.customer_id
                 left join sys_user su on su.id = o.handle_user
                 left join biz_business_manager browser_bm on browser_bm.id = COALESCE(a.bm1_id, a.business_manager_id)
                 left join biz_business_manager bm on bm.id = a.business_manager_id
                 left join biz_business_manager_channel bbmc on bbmc.id = a.bm_item_channel_id
    </sql>

    <update id="calAccountOrderTotalSpent">
        <choose>
            <when test="status == 3">
                update biz_ad_account_order a set a.total_spent=
                (select ifnull(sum(spend), 0) from biz_ad_account_insight where
                ad_account_id=a.ad_account_id and stat_date BETWEEN DATE(a.finish_time)
                AND
                CURDATE())
                where a.status=3
            </when>
            <when test="status == 5">
                update biz_ad_account_order a set a.total_spent=
                (select ifnull(sum(spend), 0) from biz_ad_account_insight where customer_id = a.customer_id and
                ad_account_id=a.ad_account_id and stat_date BETWEEN DATE(a.finish_time)
                AND
                DATE(a.recycle_time))
                where a.status=5
            </when>
        </choose>
    </update>

    <select id="selectCustomPage" resultType="top.continew.admin.biz.model.resp.AdAccountOrderResp">
        <include refid="listAdAccountOrder"/>
        ${ew.customSqlSegment}
    </select>
    <select id="selectCustomList" resultType="top.continew.admin.biz.model.resp.AdAccountOrderResp">
        <include refid="listAdAccountOrder"/>
        ${ew.customSqlSegment}
    </select>

    <sql id="AdAccountSalesStatisticsSql">
        SELECT `date`,
        SUM(sale_num) AS sale_num,
        SUM(recycle_num) AS recycle_num,
        SUM(create_num) AS create_num,
        SUM(dead_num) AS dead_num
        FROM (SELECT DATE(o.finish_time) AS `date`,
        SUM(IF(o.status = 3, 1, 0)) AS sale_num,
        SUM(IF(o.status = 5, 1, 0)) AS recycle_num,
        0 AS create_num,
        0 AS dead_num
        FROM biz_ad_account_order o
        GROUP BY `date`

        UNION ALL

        SELECT DATE(a.bm_auth_time) AS `date`,
        0 AS sale_num,
        0 AS recycle_num,
        COUNT(*) AS create_num,
        0 AS dead_num
        FROM biz_ad_account a
        where (a.keep_status = 4 and a.account_status = 1 and a.sale_status = 1) or a.sale_status = 2
        GROUP BY `date`

        UNION ALL
        SELECT DATE(a.ban_time) AS `date`,
        0 AS sale_num,
        0 AS recycle_num,
        0 AS create_num,
        COUNT(*) AS dead_num
        FROM biz_ad_account a
        where a.account_status = 2 and a.keep_status = 4 and a.ban_time is not null
        GROUP BY `date`
        ) AS combined
        <where>
            `date` is not null
            <if test="start != null and end != null">
                AND `date` between #{start} and #{end}
            </if>
        </where>
        GROUP BY `date`
        ORDER BY `date` desc
    </sql>

    <select id="selectAdAccountSalesStatistics"
            resultType="top.continew.admin.biz.model.resp.AdAccountSalesStatisticsResp">
        <include refid="AdAccountSalesStatisticsSql"/>
    </select>
    <select id="listAdAccountSalesStatistics"
            resultType="top.continew.admin.biz.model.resp.AdAccountSalesStatisticsResp">
        <include refid="AdAccountSalesStatisticsSql"/>
    </select>
    <select id="selectDashboardOverviewSale"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardOverviewCommonResp">
        SELECT (SELECT count(*) FROM biz_ad_account_order WHERE status = 3) AS total,
               (SELECT count(*)
                FROM biz_ad_account_order
                WHERE status = 3
                  and customer_id not in (select id from biz_customer where is_self_account = true)
                  AND create_time >= CURDATE()
                  AND create_time &lt; DATE_ADD(CURDATE(), INTERVAL 1 DAY)) AS today,
               (SELECT count(*)
                FROM biz_ad_account_order
                WHERE status = 3
                  AND create_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                  AND create_time &lt; CURDATE())                           AS yesterday
    </select>
    <select id="selectListDashboardAnalysisAdAccountSale"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT
        DATE_FORMAT(create_time, '%Y-%m') AS name,
        COUNT(*) AS value
        FROM biz_ad_account_order
        WHERE status = 3 and customer_id not in (select id from biz_customer where is_self_account = true) AND DATE_FORMAT(create_time, '%Y-%m' ) IN
        <foreach collection="months" item="month" separator="," open="(" close=")">
            #{month}
        </foreach>
        GROUP BY name
        ORDER BY name
    </select>
    <select id="getAdAccountSalesStatisticsSummary"
            resultType="top.continew.admin.biz.model.resp.AdAccountSalesStatisticsResp">
        SELECT
        SUM(sale_num) AS sale_num,
        SUM(recycle_num) AS recycle_num,
        SUM(create_num) AS create_num
        FROM (SELECT DATE(o.finish_time) AS `date`,
        SUM(IF(o.status = 3, 1, 0)) AS sale_num,
        SUM(IF(o.status = 5, 1, 0)) AS recycle_num,
        0 AS create_num
        FROM biz_ad_account_order o
        GROUP BY `date`

        UNION ALL

        SELECT DATE(a.bm_auth_time) AS `date`,
        0 AS sale_num,
        0 AS recycle_num,
        COUNT(*) AS create_num
        FROM biz_ad_account a
        where (a.keep_status = 4 and a.account_status = 1 and a.sale_status = 1) or a.sale_status = 2
        GROUP BY `date`) AS combined
        <where>
            <if test="start != null and end != null">
                `date` between #{start} and #{end}
            </if>
        </where>

    </select>
    <select id="selectTimezoneSaleStat"
            resultType="top.continew.admin.system.model.resp.dashboard.DashboardChartCommonResp">
        SELECT ad.timezone as name,
        COUNT(*) AS value
        FROM biz_ad_account_order o
        left join biz_ad_account ad on ad.platform_ad_id = o.ad_account_id
        WHERE o.status = 3
        <if test="statTimes != null and statTimes.length > 0">
            AND o.finish_time between #{statTimes[0]} and #{statTimes[1]}
        </if>
        GROUP BY ad.timezone
        order by ad.timezone
    </select>
    <select id="selectCustomerUnClearOrderList" resultType="top.continew.admin.biz.model.resp.AdAccountOrderResp">
        select o.ad_account_id,
               o.status,
               a.amount_spent,
               a.spend_cap
        from biz_ad_account_order o
                 left join biz_ad_account a on a.platform_ad_id = o.ad_account_id
        where o.customer_id = #{customerId}
          and o.status = 3
          and o.clear_status != 3
          and a.account_status = 1
    </select>
    <sql id="no_spent_order_sql">
        WITH account_spent AS (select ad_account_id, COALESCE(SUM(spend), 0) as insight_spend
                               from biz_ad_account_insight
                               where stat_date >= DATE_SUB(CURDATE(), INTERVAL #{day} day)
                               GROUP BY ad_account_id),
             recharge as (SELECT *
                          FROM (SELECT ro.*,
                                       ROW_NUMBER() OVER (PARTITION BY ro.platform_ad_id,ro.customer_id ORDER BY ro.id DESC) as rn
                                FROM biz_recharge_order ro
                                WHERE ro.status = 3) AS ranked_orders
                          WHERE rn = 1),
             adAccountInsight as (SELECT *
                                  FROM (SELECT baai.*,
                                               ROW_NUMBER() OVER (PARTITION BY baai.ad_account_id,baai.customer_id ORDER BY baai.id DESC) as rn
                                        FROM biz_ad_account_insight baai
                                        WHERE baai.customer_id IS NOT NULL) AS ranked_orders
                                  WHERE rn = 1),
             clearOrder as (SELECT *
                            FROM (SELECT bco.*,
                                         ROW_NUMBER() OVER (PARTITION BY bco.platform_ad_id,bco.customer_id ORDER BY bco.finish_time DESC) as rn
                                  FROM biz_clear_order bco
                                  WHERE bco.status = 3) AS ranked_orders
                            WHERE rn = 1)
        SELECT o.ad_account_id,
               o.finish_time,
               o.start_campaign_time,
               o.clear_status,
               a.timezone,
               a.id                                  as ad_account_primary_key,
               a.amount_spent,
               o.total_spent,
               a.bm_auth_time,
               c.name                                AS customer_name,
               c.id                                  as customerId,
               CONCAT_WS(',', COALESCE(a.browser_id, a.browser_no), a.bm1_browser, browser_bm.ops_browser,
                         browser_bm.reserve_browser,
                         browser_bm.reserve_browser_bak,
                         browser_bm.observe_browser) AS browser_no,
               a.bm_item_type                        AS bm_type,
               COALESCE(account_spent.insight_spend, 0) AS insight_spend,
               recharge.amount                       as rechargeAmount,
               recharge.finish_time                  as rechargeTime,
               adAccountInsight.spend                as spentAmount,
               adAccountInsight.stat_date            as spentTime,
               clearOrder.finish_time                as clearTime
        FROM biz_ad_account_order o FORCE INDEX (idx_status_finish_time)
                 LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
                 LEFT JOIN biz_customer c ON c.id = o.customer_id
                 LEFT JOIN biz_business_manager browser_bm ON browser_bm.id = COALESCE(a.bm1_id, a.business_manager_id)
                 left join biz_business_manager bm on bm.id = a.business_manager_id
                 LEFT JOIN account_spent ON account_spent.ad_account_id = o.ad_account_id
                 left join recharge
                           on o.ad_account_id = recharge.platform_ad_id and o.customer_id = recharge.customer_id
                 left join adAccountInsight on o.ad_account_id = adAccountInsight.ad_account_id and
                                               o.customer_id = adAccountInsight.customer_id
                 left join clearOrder
                           on o.ad_account_id = clearOrder.platform_ad_id and o.customer_id = clearOrder.customer_id
    </sql>
    <select id="selectNoSpentOrderPage" resultType="top.continew.admin.biz.model.resp.UnSpentOrderResp">
        <include refid="no_spent_order_sql" />
            ${ew.customSqlSegment}
    </select>
    <select id="selectNoSpentOrderList" resultType="top.continew.admin.biz.model.resp.UnSpentOrderResp">
        <include refid="no_spent_order_sql" />
        ${ew.customSqlSegment}
    </select>
    <select id="selectInsufficientBalanceOrderPage"
            resultType="top.continew.admin.biz.model.resp.InsufficientBalanceResp">

        WITH account_balance AS (SELECT platform_ad_id, COALESCE(SUM(balance), 0) AS card_balance
                                 FROM biz_card
                                 GROUP BY platform_ad_id),
             account_spent AS (select ad_account_id, COALESCE(-SUM(trans_amount), 0) as card_spent
                               from biz_card_transaction FORCE INDEX (idx_trans_time_status)
                               where trans_status != 4
                                 and stat_time >= DATE_SUB(CURDATE(), INTERVAL 1 day)
                                 and ad_account_id != ''
                               GROUP BY ad_account_id),
             recharge as(SELECT *
                         FROM (SELECT
                                   ro.*,
                                   ROW_NUMBER() OVER(PARTITION BY ro.platform_ad_id,ro.customer_id ORDER BY ro.id DESC) as rn
                               FROM
                                   biz_recharge_order ro
                               WHERE ro.status = 3
                              ) AS ranked_orders
                         WHERE
                             rn = 1),
             adAccountInsight as(SELECT *
                                 FROM (SELECT
                                           baai.*,
                                           ROW_NUMBER() OVER(PARTITION BY baai.ad_account_id,baai.customer_id ORDER BY baai.id DESC) as rn
                                       FROM
                                           biz_ad_account_insight baai
                                       WHERE baai.customer_id IS NOT NULL
                                      ) AS ranked_orders
                                 WHERE
                                     rn = 1)
        SELECT o.ad_account_id,
               o.finish_time,
               a.timezone,
               c.name                                    AS customer_name,
               c.id as customerId,
               CONCAT_WS(',', COALESCE(a.browser_id, a.browser_no), a.bm1_browser, browser_bm.ops_browser, browser_bm.reserve_browser,
                         browser_bm.reserve_browser_bak,
                         browser_bm.observe_browser)     AS browser_no,
               a.bm_item_type                            AS bm_type,
               COALESCE(account_balance.card_balance, 0) AS card_balance,
               COALESCE(account_spent.card_spent, 0)     AS card_spent,
               recharge.amount as rechargeAmount,
               recharge.finish_time as rechargeTime,
               adAccountInsight.spend as spentAmount,
               adAccountInsight.stat_date as spentTime
        FROM biz_ad_account_order o
                 LEFT JOIN biz_ad_account a ON a.platform_ad_id = o.ad_account_id
                 LEFT JOIN biz_customer c ON c.id = o.customer_id
                 LEFT JOIN account_balance ON account_balance.platform_ad_id = o.ad_account_id
                 LEFT JOIN account_spent ON account_spent.ad_account_id = o.ad_account_id
                 LEFT JOIN biz_business_manager browser_bm ON browser_bm.id = COALESCE(a.bm1_id, a.business_manager_id)
                 left join biz_business_manager bm on bm.id = a.business_manager_id
                 left join recharge on o.ad_account_id = recharge.platform_ad_id and o.customer_id = recharge.customer_id
                 left join adAccountInsight on o.ad_account_id = adAccountInsight.ad_account_id and o.customer_id = adAccountInsight.customer_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectUserOrderStat" resultType="top.continew.admin.biz.model.resp.UserOrderStatResp">
        select u.nickname, COUNT(*) as num
        from biz_ad_account_order o
        left join sys_user u on u.id = o.handle_user
        where o.status = 3
        <if test="start != null and end != null">
            AND o.finish_time BETWEEN #{start} AND #{end}
        </if>
        group by u.nickname order by num desc
    </select>
    <select id="selectStatistics" resultType="top.continew.admin.biz.model.resp.AdAccountStatisticsResp">
        SELECT
        ( SELECT nickname FROM sys_user WHERE id = baao.handle_user ) AS handleUserName,
        SUM( IF(STATUS = 3, 1, 0) ) AS authCount,
        SUM( IF(STATUS = 6, 1, 0) ) AS invalidCount,
        SUM( IF(STATUS = 7, 1, 0) ) AS authFailCount,
        SUM( IF(STATUS = 8, 1, 0) ) AS receiveFailedCount
        FROM
        biz_ad_account_order baao
        <where>
            <if test="statTimes != null and statTimes.length > 0">
                baao.finish_time between #{statTimes[0]} and #{statTimes[1]}
            </if>
        </where>
        GROUP BY
        baao.handle_user
    </select>

    <select id="sumOrderStatistics" resultType="top.continew.admin.biz.model.resp.AdAccountOrderStatisticsResp">
        SELECT
        COUNT(*) AS totalOrderCount,
        IFNULL(SUM(CASE WHEN aa.account_status = 1 THEN 1 ELSE 0 END), 0) AS normalCount,
        IFNULL(SUM(CASE WHEN aa.account_status = 2 THEN 1 ELSE 0 END), 0) AS suspendedCount,
        IFNULL(SUM(CASE WHEN o.start_campaign_time IS NULL THEN 1 ELSE 0 END), 0) AS notStartedCount,
        IFNULL(SUM(CASE WHEN (
        COALESCE(
        (
        SELECT -SUM(t.trans_amount)
        FROM biz_card_transaction t
        WHERE t.customer_id = o.customer_id AND t.ad_account_id = o.ad_account_id
        AND t.trans_status != 4
        ),
        0
        )
        ) &lt; 10 THEN 1 ELSE 0 END), 0) AS noSpendCount
        FROM biz_ad_account_order o
        LEFT JOIN biz_ad_account aa ON o.ad_account_id = aa.platform_ad_id
        LEFT JOIN biz_business_manager bm ON bm.id = aa.business_manager_id
        <where>
            o.status = 3
            <if test="query.customerId != null">
                AND o.customer_id = #{query.customerId}
            </if>
            <if test="query.bmType != null">
                AND bm.type = #{query.bmType}
            </if>
            <if test="query.bmChannelId != null">
                AND bm.channel_id = #{query.bmChannelId}
            </if>
            <if test="query.timezone != null and query.timezone != ''">
                AND aa.timezone = #{query.timezone}
            </if>
            <if test="query.payTime != null and query.payTime.length== 2">
                AND o.finish_time BETWEEN #{query.payTime[0]} AND #{query.payTime[1]}
            </if>
        </where>
    </select>
    <select id="getCustomerFbBalance" resultType="java.math.BigDecimal">
        select COALESCE(SUM(IF(enable_prepay, (recharge_amount - total_spent), (recharge_amount - amount_spent))),
                        0) as fb_balance
        from (SELECT o.customer_id,
                     o.enable_prepay,
                     o.total_spent,
                     ad.spend_cap,
                     ad.amount_spent,
                     IF(o.enable_prepay = true, (select COALESCE(sum(IF(r.type =
                                                                        3, r.amount, 0)), 0) -
                                                        COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0)
                                                 from biz_customer_balance_record r
                                                 where r.platform_ad_id = o.ad_account_id
                                                   and r.customer_id = o.customer_id
                                                   AND r.trans_time >= o.pay_time), ad.spend_cap) as recharge_amount
              from biz_ad_account_order o
                       left join
                   biz_ad_account ad on ad.platform_ad_id =
                                        o.ad_account_id
              where o.customer_id = #{customerId}
                and o.status = 3
                and o.clear_status != 3) temp;
    </select>
    <select id="getTotalFbBalance" resultType="java.math.BigDecimal">
        select COALESCE(SUM(IF(enable_prepay, (recharge_amount - total_spent), (recharge_amount - amount_spent))), 0) as fb_balance
        from (SELECT o.customer_id,
                     o.enable_prepay,
                     o.total_spent,
                     ad.spend_cap,
                     ad.amount_spent,
                     IF(o.enable_prepay = true, (select COALESCE(sum(IF(r.type =
                                                                        3, r.amount, 0)), 0) -
                                                        COALESCE(sum(IF(r.type in (4, 5), r.amount, 0)), 0)
                                                 from biz_customer_balance_record r
                                                 where r.platform_ad_id = o.ad_account_id
                                                   and r.customer_id = o.customer_id
                                                   AND r.trans_time >= o.pay_time), ad.spend_cap) as recharge_amount
              from biz_ad_account_order o
                       left join
                   biz_ad_account ad on ad.platform_ad_id =
                                        o.ad_account_id
                       left join biz_customer c on c.id = o.customer_id
              where o.status = 3 and o.order_method = 1
                and c.is_self_account = false
                and c.type = 1
                and c.settle_type = #{settleType}
                and c.business_type = 1
                and o.clear_status != 3) temp;
    </select>
    <select id="selectCompleteAndNormalAdAccountOrderList"
            resultType="top.continew.admin.biz.model.entity.AdAccountOrderDO">
        select o.*
        from biz_ad_account_order o
                 left join biz_ad_account ad on ad.platform_ad_id = o.ad_account_id
        where o.status = 3
          and ad.account_status = 1
    </select>
    <select id="getCustomerDailyStat" resultType="top.continew.admin.biz.model.resp.CustomerDailyStatResp">
        SELECT o.customer_id,
               COUNT(*)                       AS total_account,
               IFNULL(SUM(CASE
                              WHEN o.status = 3 or (o.status = 5 and DATE(o.recycle_time) > #{statDate}) THEN 1
                              ELSE 0 END), 0) AS total_finish_account,
               IFNULL(SUM(CASE
                              WHEN (o.status = 3 or (o.status = 5 and DATE(o.recycle_time) > #{statDate})) and
                                   (aa.account_status = 1 or
                                    (aa.account_status = 2 and DATE(aa.ban_time) > #{statDate})) THEN 1
                              ELSE 0 END),
                      0)                      AS total_normal_account,
               IFNULL(SUM(CASE
                              WHEN o.status = 3 and aa.account_status = 2 and DATE(aa.ban_time) = #{statDate} THEN 1
                              ELSE 0 END), 0) AS today_ban_account,
               IFNULL(SUM(CASE
                              WHEN DATE(o.finish_time) = #{statDate} THEN 1
                              ELSE 0 END), 0) AS today_open_account
        FROM biz_ad_account_order o
                 LEFT JOIN biz_ad_account aa ON o.ad_account_id = aa.platform_ad_id
        where o.status in (3, 5)
          and DATE(o.finish_time) &lt;= #{statDate}
        group by o.customer_id
    </select>
    <select id="selectInsufficientBalanceAdAccount"
            resultType="top.continew.admin.biz.model.resp.AdAccountOrderResp">
        select o.id, o.ad_account_id, o.customer_id, o.balance_alert_threshold, o.has_balance_alert, ad.spend_cap, ad.amount_spent, ad.name as ad_account_name
        from biz_ad_account_order o
                 left join biz_ad_account ad on ad.platform_ad_id = o.ad_account_id
        where o.status = 3
          and o.enable_prepay = false
          and o.order_method = 1
          and o.clear_status = 1
          and o.balance_alert_threshold > -1
          and o.has_balance_alert = false
          and ad.account_status = 1
    </select>


</mapper>