/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import top.continew.admin.biz.enums.AdAccountClearStatusEnum;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.starter.extension.crud.model.entity.BaseDO;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 下户订单实体
 *
 * <AUTHOR>
 * @since 2024/12/30 17:59
 */
@Data
@TableName("biz_ad_account_order")
public class AdAccountOrderDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 关联广告户
     */
    private String adAccountId;

    private String adAccountName;

    /**
     * 客户BM ID
     */
    private String customerBmId;

    /**
     * 开户费
     */
    private BigDecimal payAmount;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 终止时间
     */
    private LocalDateTime endTime;

    /**
     * 状态
     */
    private AdAccountOrderStatusEnum status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理人
     */
    private Long handleUser;

    /**
     * 使用人
     */
    private Long usedUserId;

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;

    /**
     * 授权时间
     */
    private LocalDateTime finishTime;

    /**
     * 回收时间
     */
    private LocalDateTime recycleTime;

    /**
     * 总消耗
     */
    private BigDecimal totalSpent;

    /**
     * 下户成本
     */
    private BigDecimal cost;

    /**
     * 是否已退款
     */
    private Boolean refunded;

    /**
     * 上系列时间
     */
    private LocalDateTime startCampaignTime;

    /**
     * 第一次上系列时间
     */
    private LocalDateTime firstStartCampaignTime;

    /**
     * 客户需求ID
     */
    private Long customerRequirementId;

    private Boolean enablePrepay;

    private Boolean isOneDollar;

    private String customerEmail;

    private Boolean takeStatus;

    private Long groupId;

    private Integer costParty;

    private AdAccountClearStatusEnum clearStatus;

    private LocalDateTime clearTime;

    /**
     * 关联商务用户ID
     */
    private Long businessUserId;
    private Integer orderMethod;

    //余额提醒金额阈值
    private BigDecimal balanceAlertThreshold;

    private Boolean hasBalanceAlert;
}