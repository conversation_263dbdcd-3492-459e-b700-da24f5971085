/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.AdPlatformEnum;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.enums.CardStatusEnum;
import top.continew.admin.biz.event.AdAccountBindCardEvent;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.CardMapper;
import top.continew.admin.biz.model.entity.CardBalanceDO;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.query.CardQuery;
import top.continew.admin.biz.model.req.CardOpenReq;
import top.continew.admin.biz.model.req.CardPlatformUpdateReq;
import top.continew.admin.biz.model.req.CardRechargeReq;
import top.continew.admin.biz.model.req.CardReq;
import top.continew.admin.biz.model.resp.CardDetailResp;
import top.continew.admin.biz.model.resp.CardResp;
import top.continew.admin.biz.service.CardBalanceService;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.biz.utils.RandomEnglishNameGenerator;
import top.continew.admin.common.constant.CacheConstants;
import top.continew.admin.common.context.UserContextHolder;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.cache.redisson.util.RedisUtils;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 卡片业务实现
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CardServiceImpl extends BaseServiceImpl<CardMapper, CardDO, CardResp, CardDetailResp, CardQuery, CardReq> implements CardService {

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    private final CardBalanceService cardBalanceService;

    private final UserService userService;

    private final RedisTemplate<String, String> redisTemplate;

    @Override
    public PageResp<CardResp> page(CardQuery query, PageQuery pageQuery) {
        QueryWrapper<CardDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, pageQuery);
        IPage<CardResp> page = this.baseMapper.selectCustomPage(new Page<>((long)pageQuery.getPage(), (long)pageQuery.getSize()), queryWrapper);
        return new PageResp<>(page.getRecords(), page.getTotal());
    }

    @Override
    protected <E> List<E> list(CardQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<CardDO> queryWrapper = this.buildQueryWrapper(query);
        this.sort(queryWrapper, sortQuery);
        List<CardResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    protected void beforeUpdate(CardReq req, Long id) {
        if (StringUtils.isNotBlank(req.getPlatformAdId())) {
            req.setPlatformAdId(StringUtils.trim(req.getPlatformAdId()));
        }
    }

    @Override
    protected void afterUpdate(CardReq req, CardDO entity) {
        if (StringUtils.isNotBlank(entity.getPlatformAdId()) && entity.getAdPlatform()
            .equals(AdPlatformEnum.FACEBOOK)) {
            AdAccountBindCardEvent.AdAccountBindCardModel adAccountBindCardModel = new AdAccountBindCardEvent.AdAccountBindCardModel(entity.getCardNumber(), req.getRemark(), entity.getPlatform());
            SpringUtil.publishEvent(new AdAccountBindCardEvent(adAccountBindCardModel));
        }
        RedisUtils.delete(CacheConstants.CARD_KEY_PREFIX + entity.getCardNumber());
    }

    @Override
    protected QueryWrapper<CardDO> buildQueryWrapper(CardQuery query) {
        QueryWrapper<CardDO> queryWrapper = super.buildQueryWrapper(query);
        if (query.getCustomerId() != null) {
            queryWrapper.exists("SELECT 1 FROM biz_ad_account_order o WHERE o.ad_account_id = card.platform_ad_id AND o.status IN (3, 5) AND o.customer_id = {0}", query.getCustomerId());
        }
        return queryWrapper;
    }

    @Override
    public List<CardDO> listByPlatform(Integer platform) {
        return this.list(Wrappers.<CardDO>lambdaQuery().eq(CardDO::getPlatform, platform));
    }

    @Override
    public void syncData(Integer limitPage) {
        for (CardPlatformEnum value : CardPlatformEnum.values()) {
            if (!value.isEnable()) {
                continue;
            }

            try {
                this.syncData(value, limitPage);
            } catch (Exception e) {
                log.error("【{}】卡片数据同步失败：{}", value.getDescription(), ExceptionUtils.getStackTrace(e));
            }
        }
    }

    @Override
    public CardDO getByCardNumber(String cardNumber, boolean ignoreNull) {
        CardDO cardDO = this.getOne(Wrappers.<CardDO>lambdaQuery().eq(CardDO::getCardNumber, cardNumber));
        CheckUtils.throwIf(!ignoreNull && cardDO == null, "未找到相关卡片数据");
        return cardDO;
    }

    @Override
    public CardDO getByCardNumberByCache(String cardNumber) {
        return this.getOne(Wrappers.<CardDO>lambdaQuery().eq(CardDO::getCardNumber, cardNumber));
    }

    @Override
    public BigDecimal getPlatformBalance(CardPlatformEnum platform) {
        CardBalanceDO cardBalance = cardBalanceService.getOne(Wrappers.<CardBalanceDO>lambdaQuery()
            .eq(CardBalanceDO::getPlatform, platform)
            .orderByDesc(CardBalanceDO::getTransTime)
            .last("limit 1"));
        return cardBalance == null ? BigDecimal.ZERO : cardBalance.getAfterAmount();
    }

    @Override
    public BigDecimal getCardTotalBalance() {
        return this.getObj(Wrappers.<CardDO>query().select("sum(balance)"), v -> v == null
            ? BigDecimal.ZERO
            : new BigDecimal(String.valueOf(v)));
    }

    @Override
    public String getCardVerifyCode(String cardNumber) {
        CardDO cardDO = this.getByCardNumber(cardNumber.trim(), false);
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(cardDO.getPlatform());
        return cardOpsStrategy.getVerifyCode(cardDO);
    }

    @Override
    public void updateRemark(CardPlatformUpdateReq req) {
        CardDO exist = this.getByCardNumber(req.getCardNumber().trim(), false);
        RedisUtils.delete(CacheConstants.CARD_KEY_PREFIX + exist.getCardNumber());
        AdAccountBindCardEvent.AdAccountBindCardModel adAccountBindCardModel = new AdAccountBindCardEvent.AdAccountBindCardModel(exist.getCardNumber(), req.getRemark(), exist.getPlatform());
        SpringUtil.publishEvent(new AdAccountBindCardEvent(adAccountBindCardModel));
    }

    @Override
    public void recharge(CardRechargeReq req) {
        CardDO cardDO = this.getByCardNumber(req.getCardNumber().trim(), false);
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(cardDO.getPlatform());
        cardOpsStrategy.rechargeCard(cardDO, req.getAmount());

        //更新量子卡的余额
        if(CardPlatformEnum.INTERLACE.equals(cardDO.getPlatform())) {
            BigDecimal balance = cardOpsStrategy.getCardBalance(cardDO);
            if(null != balance) {
                CardDO updateCard = new CardDO();
                updateCard.setId(cardDO.getId());
                updateCard.setBalance(balance);
                baseMapper.updateById(updateCard);
            }

        }
    }

    @Override
    public List<LabelValueResp<String>> getCardBinList(CardPlatformEnum platformEnum) {
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(platformEnum);
        return cardOpsStrategy.getCardBinList();
    }

    @Override
    public CardDO open(CardOpenReq req) {
        try {

            CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(req.getPlatform());
            JSONObject openCardReq = JSONObject.from(req);
            //光子易需要设置持卡人ID以及requestId
            if (CardPlatformEnum.PHOTON_PAY.equals(req.getPlatform())) {
                if (StrUtil.isBlank(req.getCardScheme())) {
                    throw new BusinessException("卡组织不能为空");
                }

                UserDO user = userService.getById(StpUtil.getLoginIdAsLong());
                if (null == user || StringUtils.isBlank(user.getGzyCardHolderId())) {
                    throw new BusinessException("没有配置卡台的用卡人");
                }

                String requestId = "GZY_" + IdUtil.getSnowflakeNextIdStr();
                openCardReq.put("requestId", requestId);
                openCardReq.put("cardHolderId", user.getGzyCardHolderId());
            }
            CardDO cardDO;
            if(CardPlatformEnum.INTERLACE.equals(req.getPlatform())){
               cardDO = getInterlaceUnUsedCard(req.getPlatform(), req.getCardBin());

            }else {
                cardDO = cardOpsStrategy.openCard(openCardReq);
            }


            if (null != cardDO) {
                cardDO.setCardName(RandomEnglishNameGenerator.generateRandomName());
                cardDO.setCreateUser(UserContextHolder.getUserId());
                this.saveOrUpdate(cardDO);
            }

            return cardDO;

        } catch (Exception e) {
            throw new BusinessException("开卡失败：" + e.getMessage());
        }
    }

    @Override
    public CardDO getInterlaceUnUsedCard(CardPlatformEnum platformEnum, String cardBin) {
        String lockKey = "card:getInterlaceUnUsedCard:" + platformEnum.getValue() + ":" + cardBin;
        long expireTime = 10000; // 锁的过期时间，10秒
        long timeout = 5000; // 获取锁的超时时间，5秒

        boolean locked = RedisUtils.tryLock(lockKey, expireTime, timeout);
        if (!locked) {
            throw new BusinessException("获取分布式锁失败，请稍后重试");
        }

        try {
            // 1. 到数据库查找未使用的一张卡片（平台是量子卡台）
            CardDO cardDO = baseMapper.selectUnUsedCard(platformEnum.getValue(), cardBin);
            CheckUtils.throwIf(cardDO == null, "没有可用的卡片");
            // 2. 将卡片更新为已使用
            cardDO.setHasUsed(true);
            // 增加条件检查，确保更新时卡片仍然未使用
            boolean updated = this.update(cardDO, Wrappers.<CardDO>lambdaUpdate()
                    .eq(CardDO::getId, cardDO.getId())
                    .eq(CardDO::getHasUsed, false));
            CheckUtils.throwIf(!updated, "卡片已被其他线程使用");

            return cardDO;
        } finally {
            RedisUtils.unlock(lockKey);
        }
    }

    @Override
    public CardDO getApplyCardResult(CardPlatformEnum platformEnum, String requestId) {
        List<CardDO> oldCards = baseMapper.selectList(new LambdaQueryWrapper<CardDO>().eq(CardDO::getCardNumber, requestId)
            .eq(CardDO::getPlatform, platformEnum));
        if (CollUtil.isEmpty(oldCards)) {
            throw new BusinessException("不存在申请中的卡片");
        }

        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategy(platformEnum);
        CardDO cardDO = cardOpsStrategy.getApplyCardResult(requestId);

        if (null != cardDO) {
            cardDO.setId(oldCards.get(0).getId());
            cardDO.setCardName(oldCards.get(0).getCardName());
            UserDO user = userService.getById(StpUtil.getLoginIdAsLong());
            if (CardPlatformEnum.PHOTON_PAY.equals(platformEnum)) {
                cardDO.setPlatformCardHolderId(null != user ? user.getGzyCardHolderId() : null);
            }
            this.updateById(cardDO);
        }

        return cardDO;
    }

    @Override
    public String getCardNumber(CardPlatformEnum platformEnum, String platformCardId) {
        //存储于redis缓存中，先从缓存中查询
        String cardNumber = RedisUtils.get(platformEnum.getValue() + "_" + platformCardId);

        if (StrUtil.isNotBlank(cardNumber)) {
            return cardNumber;
        }

        CardDO cardDO = this.getOne(Wrappers.<CardDO>lambdaQuery()
            .eq(CardDO::getPlatformCardId, platformCardId)
            .eq(CardDO::getPlatform, platformEnum.getValue())
            .last("limit 1"));
        if (cardDO != null && StringUtils.isNotBlank(cardDO.getCardNumber()) && !cardDO.getCardNumber()
            .contains("****")) {
            cardNumber = cardDO.getCardNumber();
        }

        //存储于缓存中
        if (StrUtil.isNotBlank(cardNumber)) {
            RedisUtils.set(platformEnum.getValue() + "_" + platformCardId, cardNumber, Duration.ofDays(30));
        }

        return cardNumber;
    }

    @Override
    public CardDO getByPlatformCardId(CardPlatformEnum platformEnum, String platformCardId) {
        return this.getOne(Wrappers.<CardDO>lambdaQuery()
            .eq(CardDO::getPlatformCardId, platformCardId)
            .eq(CardDO::getPlatform, platformEnum.getValue()));
    }

    @Override
    public void loadCardSensitiveInfo(CardPlatformEnum platformEnum) {
        //获取到光子易的卡片
        List<CardDO> list = baseMapper.selectList(new LambdaQueryWrapper<CardDO>().eq(CardDO::getPlatform, platformEnum)
            .eq(CardDO::getCvv, ""));

        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(platformEnum);

        for (CardDO cardDO : list) {
            try {
                CardDO sensitiveDetail = cardOpsStrategy.getCardSensitiveDetail(cardDO.getPlatformCardId());
                if (StrUtil.isNotBlank(sensitiveDetail.getCardNumber())) {
                    CardDO updateCard = new CardDO();
                    updateCard.setCardNumber(sensitiveDetail.getCardNumber());
                    updateCard.setCvv(sensitiveDetail.getCvv());
                    updateCard.setExpireDate(sensitiveDetail.getExpireDate());
                    updateCard.setId(cardDO.getId());
                    baseMapper.updateById(updateCard);
                }
            } catch (Exception e) {
                log.error("loadGzyCardSensitiveInfo error," + cardDO.getPlatformCardId(), e);
            }
        }
    }

    @Override
    public void updateCardUsedAmount(CardPlatformEnum platform) {
        baseMapper.updateCardUsedAmount(platform.getValue());
    }

    @Override
    public void loadGzyCardHolderId() {
        List<CardDO> list = baseMapper.selectList(new LambdaQueryWrapper<CardDO>().eq(CardDO::getPlatform, CardPlatformEnum.PHOTON_PAY)
            .eq(CardDO::getPlatformCardHolderId, "")
            .last("limit 30"));

        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(CardPlatformEnum.PHOTON_PAY);
        for (CardDO cardDO : list) {
            try {
                CardDO gzyCardDO = cardOpsStrategy.getCardDetail(cardDO.getPlatformCardId());
                if (null != gzyCardDO && StrUtil.isNotBlank(gzyCardDO.getPlatformCardHolderId())) {
                    CardDO updateCard = new CardDO();
                    updateCard.setPlatformCardHolderId(gzyCardDO.getPlatformCardHolderId());
                    updateCard.setId(cardDO.getId());
                    baseMapper.updateById(updateCard);

                }
            } catch (Exception e) {
                log.error("loadGzyCardHolderId error," + cardDO.getPlatformCardId(), e);
            }

        }
    }

    @Override
    public List<CardResp> getNeedWithdrawCard() {
        return this.baseMapper.getNeedWithdrawCard();
    }

    @Override
    public void active(Long id) {
        CardDO cardDO = this.getById(id);
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(cardDO.getPlatform());
        if (cardDO.getPlatform().equals(CardPlatformEnum.PHOTON_PAY)) {
            CardDO detail = cardOpsStrategy.getCardDetail(cardDO.getPlatformCardId());
            if (detail.getStatus().equals(CardStatusEnum.NORMAL)) {
                CardDO update = new CardDO();
                update.setId(id);
                update.setStatus(CardStatusEnum.NORMAL);
                this.updateById(update);
                return;
            }
        }
        try {
            cardOpsStrategy.activeCard(cardDO);
            CardDO update = new CardDO();
            update.setId(id);
            update.setStatus(CardStatusEnum.NORMAL);
            this.updateById(update);
        } catch (Exception e) {
            log.error("{}激活失败：{}", cardDO.getCardNumber(), e.getMessage());
        }
    }

    @Override
    public void inactive(Long id) {
        CardDO cardDO = this.getById(id);
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(cardDO.getPlatform());
        if (cardDO.getPlatform().equals(CardPlatformEnum.PHOTON_PAY)) {
            CardDO detail = cardOpsStrategy.getCardDetail(cardDO.getPlatformCardId());
            if (detail.getStatus().equals(CardStatusEnum.FROZEN)) {
                CardDO update = new CardDO();
                update.setId(id);
                update.setStatus(CardStatusEnum.FROZEN);
                this.updateById(update);
                return;
            }
        }
        try {
            cardOpsStrategy.inactiveCard(cardDO);
            CardDO update = new CardDO();
            update.setId(id);
            update.setStatus(CardStatusEnum.FROZEN);
            this.updateById(update);
        } catch (Exception e) {
            log.error("{}冻结失败：{}", cardDO.getCardNumber(), e.getMessage());
        }
    }

    @Override
    public void active(String cardNumber) {
        CardDO cardDO = this.getByCardNumber(cardNumber, true);
        if (cardDO != null) {
            this.active(cardDO.getId());
        }
    }

    @Override
    public void inactive(String cardNumber) {
        CardDO cardDO = this.getByCardNumber(cardNumber, true);
        if (cardDO != null) {
            this.inactive(cardDO.getId());
        }
    }

    @Override
    public void addCardBalance(Long id, BigDecimal balance) {
        if (balance.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        this.update(Wrappers.<CardDO>lambdaUpdate().eq(CardDO::getId, id).setSql("balance = balance + " + balance));
    }

    @Override
    public void subCardBalance(Long id, BigDecimal balance) {
        if (balance.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        this.update(Wrappers.<CardDO>lambdaUpdate().eq(CardDO::getId, id).setSql("balance = balance - " + balance));
    }

    @Override
    public void syncCardBalance(Long id) {
        CardDO cardDO = this.getById(id);
        CardOpsStrategy cardVpOpsStrategy = cardOpsStrategyFactory.findStrategy(cardDO.getPlatform());
        if (CardStatusEnum.PENDING.equals(cardDO.getStatus())) {
            getApplyCardResult(cardDO.getPlatform(), cardDO.getCardNumber());
        } else {
            BigDecimal balance = cardVpOpsStrategy.getCardBalance(cardDO);
            CardDO update = new CardDO();
            update.setId(id);
            update.setBalance(balance);
            this.updateById(update);
        }

    }

    @Override
    public void updateUsedStatus(Long id, Boolean hasUsed) {
        CardDO cardDO = this.getById(id);
        if (cardDO != null) {
            CardDO update = new CardDO();
            update.setId(id);
            update.setHasUsed(hasUsed);
            this.updateById(update);
        }
    }

    @Override
    public List<String> cardHeadList() {
        return baseMapper.cardHeadList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @Async
    public void syncData(CardPlatformEnum platformEnum, Integer limitPage) {
        CardOpsStrategy cardOpsStrategy = cardOpsStrategyFactory.findStrategyIgnoreNull(platformEnum);
        if (cardOpsStrategy != null) {
            List<CardDO> saveList = new ArrayList<>();
            List<CardDO> updateList = new ArrayList<>();
            List<CardDO> cardList = cardOpsStrategy.getCardList(null, null, limitPage);
            List<CardDO> existList = this.listByPlatform(platformEnum.getValue());
            for (CardDO cardDO : cardList) {
                boolean isInterlace = CardPlatformEnum.INTERLACE.equals(platformEnum);


                CardDO existCard = (!CardPlatformEnum.PHOTON_PAY.equals(platformEnum) && !isInterlace)
                    ? existList.stream()
                    .filter(v -> v.getCardNumber().equalsIgnoreCase(cardDO.getCardNumber()))
                    .findFirst()
                    .orElse(null)
                    : existList.stream()
                        .filter(v -> v.getPlatformCardId().equalsIgnoreCase(cardDO.getPlatformCardId()))
                        .findFirst()
                        .orElse(null);

                if(isInterlace) {
                    //新增卡时，去查询余额。更新卡时，只有当全量同步时再去查询余额
                    if(existCard == null || limitPage == null) {
                        BigDecimal balance = cardOpsStrategy.getCardBalance(cardDO);
                        if(null != balance) {
                            cardDO.setBalance(balance);
                        }
                    }
                }

                if (existCard != null) {
                    boolean needUpdate = StringUtils.isNotBlank(cardDO.getRemark()) && !cardDO.getRemark()
                        .equals(existCard.getRemark());
                    if (StringUtils.isNotBlank(cardDO.getCardName()) && !cardDO.getCardName()
                        .equals(existCard.getCardName())) {
                        needUpdate = true;
                    }
                    if (existCard.getOpenTime() == null) {
                        needUpdate = true;
                    }
                    if (cardDO.getBalance() != null && existCard.getBalance() != null && cardDO.getBalance()
                        .compareTo(existCard.getBalance()) != 0) {
                        needUpdate = true;
                    }
                    if (!cardDO.getStatus().equals(existCard.getStatus())) {
                        needUpdate = true;
                    }
                    if (cardDO.getUsedAmount() != null && existCard.getUsedAmount() != null && cardDO.getUsedAmount()
                        .compareTo(existCard.getUsedAmount()) != 0) {
                        needUpdate = true;
                    }
                    if (needUpdate) {
                        CardDO update = new CardDO();
                        update.setId(existCard.getId());
                        update.setExpireDate(cardDO.getExpireDate());
                        if (!CardPlatformEnum.PHOTON_PAY.equals(platformEnum)) {
                            update.setBalance(cardDO.getBalance());
                            update.setUsedAmount(cardDO.getUsedAmount());
                        }

                        update.setStatus(cardDO.getStatus());
                        update.setRemark(cardDO.getRemark());
                        update.setCardName(cardDO.getCardName());
                        update.setCvv(cardDO.getCvv());
                        update.setOpenTime(cardDO.getOpenTime());
                        updateList.add(update);
                    }
                } else {
                    cardDO.setBalance(null != cardDO.getBalance() ? cardDO.getBalance() : BigDecimal.ZERO);

                    saveList.add(cardDO);
                }
            }
            this.saveBatch(saveList);
            this.updateBatchById(updateList);
        }
    }
}