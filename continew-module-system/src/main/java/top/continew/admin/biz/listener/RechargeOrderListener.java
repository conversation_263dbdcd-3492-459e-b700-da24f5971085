/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.listener;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.methods.send.SendPhoto;
import org.telegram.telegrambots.meta.api.objects.InputFile;
import top.continew.admin.biz.enums.AdAccountOrderStatusEnum;
import top.continew.admin.biz.enums.CustomerBalanceTypeEnum;
import top.continew.admin.biz.enums.RechargeOrderCardStatusEnum;
import top.continew.admin.biz.enums.RechargeOrderStatusEnum;
import top.continew.admin.biz.event.*;
import top.continew.admin.biz.model.entity.AdAccountOrderDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.entity.RechargeOrderDO;
import top.continew.admin.biz.model.req.RechargeOrderFinishReq;
import top.continew.admin.biz.service.*;
import top.continew.admin.system.service.FileService;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Component
@Slf4j
@RequiredArgsConstructor
public class RechargeOrderListener {

    private final CustomerService customerService;

    private final AdAccountService adAccountService;

    private final RechargeOrderService rechargeOrderService;

    private final CustomerBalanceRecordService customerBalanceRecordService;

    private final AdAccountOrderService adAccountOrderService;

    private final FileService fileService;

    @EventListener(RechargeOrderHandleEvent.class)
    public void handle(RechargeOrderHandleEvent event) {
        //        Long orderId = (Long)event.getSource();
        //        RechargeOrderDO order = rechargeOrderService.getById(orderId);
        //        String currentUser = UserContextHolder.getUsername();
        //        log.info("【充值订单】{}已被{}接单", orderId, currentUser);
        //        String message = order.getPlatformAdId() + " 正在充值中";
        //        CustomerDO customer = customerService.getById(order.getCustomerId());
        //        SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
        //            .chatId(customer.getTelegramChatId())
        //            .replyToMessageId(order.getApplyMessageId())
        //            .text(message)
        //            .build()));
    }

    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void finish(RechargeOrderFinishEvent event) {
        RechargeOrderFinishReq req = (RechargeOrderFinishReq)event.getSource();
        Long orderId = req.getId();
        rechargeOrderService.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            .set(StringUtils.isNotBlank(req.getCertificate()), RechargeOrderDO::getCertificate, req.getCertificate())
            .set(StringUtils.isNotBlank(req.getRemark()), RechargeOrderDO::getRemark, req.getRemark())
            .set(RechargeOrderDO::getFinishTime, LocalDateTime.now())
            .set(RechargeOrderDO::getStatus, RechargeOrderStatusEnum.FINISH)
            .eq(RechargeOrderDO::getId, req.getId()));
        RechargeOrderDO order = rechargeOrderService.getById(req.getId());
        CustomerDO customer = customerService.getById(order.getCustomerId());
        log.info("【充值订单】{}已完成", orderId);
        customerService.changeAmount(order.getCustomerId(), order.getPlatformAdId(), order.getAmount()
            .negate(), CustomerBalanceTypeEnum.AD_ACCOUNT_RECHARGE, null, null);
        log.info("【充值订单】{}客户扣款成功", orderId);
        String replyMsg = "%s 已充值 %s".formatted(order.getPlatformAdId(), NumberUtil.toStr(order.getAmount()));
        InputFile inputFile = fileService.getFileInputFile(order.getCertificate());
        if (inputFile != null) {
            SendPhoto sendPhoto = SendPhoto.builder()
                .chatId(customer.getTelegramChatId())
                .photo(inputFile)
                .caption(replyMsg)
                .build();
            SpringUtil.publishEvent(new TelegramMessageEvent(sendPhoto));
        } else {
            SpringUtil.publishEvent(new TelegramMessageEvent(SendMessage.builder()
                .chatId(customer.getTelegramChatId())
                .text(replyMsg)
                .build()));
        }
        // 发送额度汇总信息
        BigDecimal totalTransfer = customerBalanceRecordService.getTotalTransferAmount(customer.getId());
        CustomerBalanceChangeModel changeModel = new CustomerBalanceChangeModel(customer, totalTransfer, null, customer.getBalance(), order.getAmount()
            .negate());
        SpringUtil.publishEvent(new CustomerBalanceChangeEvent(changeModel));
        // 更新余额不足提醒状态
        adAccountOrderService.update(Wrappers.<AdAccountOrderDO>lambdaUpdate()
            .set(AdAccountOrderDO::getHasBalanceAlert, false)
            .eq(AdAccountOrderDO::getAdAccountId, order.getPlatformAdId())
            .eq(AdAccountOrderDO::getCustomerId, order.getCustomerId())
            .eq(AdAccountOrderDO::getStatus, AdAccountOrderStatusEnum.AUTH_COMPLETED));
        adAccountOrderService.authorizeSuccessByRecharge(order.getPlatformAdId());
    }

    @EventListener
    public void cancel(RechargeOrderCancelEvent event) {
        Long orderId = (Long)event.getSource();
        RechargeOrderDO order = rechargeOrderService.getById(orderId);
        log.info("【充值订单】{}已被取消", orderId);
        rechargeOrderService.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            .set(RechargeOrderDO::getStatus, RechargeOrderStatusEnum.CANCEL)
            .eq(RechargeOrderDO::getId, orderId));
        if (order.getCardStatus().equals(RechargeOrderCardStatusEnum.AUTO)) {
            // 取出充值金额
            //            AdAccountCardOpsResultResp resultResp = adAccountService.withdrawMasterCard(order.getPlatformAdId(), order.getAmount());
            //            if (resultResp.getIsSuccess()) {
            //                rechargeOrderService.update(Wrappers.<RechargeOrderDO>lambdaUpdate()
            //                    .set(RechargeOrderDO::getCardStatus, RechargeOrderCardStatusEnum.REVOKE)
            //                    .eq(RechargeOrderDO::getId, orderId));
            //            }
        }
    }
}
