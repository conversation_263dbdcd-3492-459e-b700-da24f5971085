/*
 * Copyright (c) 2022-present <PERSON><PERSON><PERSON> <PERSON>s. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.robot.strategy.impl;

import org.springframework.stereotype.Service;
import org.telegram.telegrambots.meta.api.objects.Update;
import top.continew.admin.biz.robot.enums.RobotCommandEnum;
import top.continew.admin.biz.robot.strategy.RobotCommandStrategy;

@Service
public class RobotGetWalletAddressStrategyImpl implements RobotCommandStrategy {
    @Override
    public RobotCommandEnum getCommand() {
        return RobotCommandEnum.WALLET_ADDRESS;
    }

    @Override
    public String execute(Update update) {
        return "TBipLtwBVkfcP7ZGPp8k5oU3NkFEq2tNtJ";
    }
}
