package top.continew.admin.biz.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.katai.CardOpsStrategyFactory;
import top.continew.admin.biz.katai.strategy.CardOpsStrategy;
import top.continew.admin.biz.mapper.CardBinMapper;
import top.continew.admin.biz.model.entity.CardBinDO;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.req.CardBinReq;
import top.continew.admin.biz.model.resp.CardBinDetailResp;
import top.continew.admin.biz.model.resp.CardBinResp;
import top.continew.admin.biz.service.CardBinService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 卡头管理业务实现
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CardBinServiceImpl extends BaseServiceImpl<CardBinMapper, CardBinDO, CardBinResp, CardBinDetailResp, CardBinQuery, CardBinReq> implements CardBinService {

    private final CardOpsStrategyFactory cardOpsStrategyFactory;

    @Override
    public PageResp<CardBinResp> page(CardBinQuery query, PageQuery pageQuery) {

        if (query.getDay() == null || query.getDay() < 7) {
            query.setDay(7);
        }

        IPage<CardBinResp> page = baseMapper.customerPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), query);

        // page.getRecords().forEach(item -> {
        //     // 处理交易金额
        //     CardBinResp res = baseMapper.selectTransAmount(item.getId(), query.getDay());
        //     item.setTotalTransactionAmount(res.getTotalTransactionAmount());
        //     item.setRecentTransactionAmount(res.getRecentTransactionAmount());
        // });

        return PageResp.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCardBin(CardPlatformEnum platform) {
        log.info("开始同步卡头数据，平台：{}", platform.getDescription());

        try {
            CardOpsStrategy<?> cardOpsStrategy = cardOpsStrategyFactory.findStrategy(platform);
            List<LabelValueResp<String>> cardBinList = cardOpsStrategy.getCardBinList();

            if (CollectionUtil.isEmpty(cardBinList)) {
                log.warn("平台 {} 未获取到卡头数据", platform.getDescription());
                return;
            }

            // 过滤和转换数据
            List<CardBinDO> cardBinDOList = cardBinList.stream()
                    .filter(res -> !Objects.equals("AUTO", res.getValue()))
                    .filter(res -> StrUtil.isNotBlank(res.getValue()))
                    .map(res -> {
                        CardBinDO cardBinDO = new CardBinDO();
                        cardBinDO.setCardBin(res.getValue());
                        cardBinDO.setPlatform(platform);
                        cardBinDO.setName(res.getValue());

                        // 安全的类型转换
                        String cardScheme = extractCardScheme(res.getExtra());
                        cardBinDO.setCardScheme(cardScheme);
                        cardBinDO.setEnable(true); // 默认启用

                        return cardBinDO;
                    })
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(cardBinDOList)) {
                log.warn("平台 {} 过滤后无有效卡头数据", platform.getDescription());
                return;
            }

            // 逐个插入，忽略重复数据
            int successCount = 0;
            for (CardBinDO cardBinDO : cardBinDOList) {
                try {
                    this.save(cardBinDO);
                    successCount++;
                } catch (DuplicateKeyException e) {
                    // 忽略重复数据
                    log.debug("卡头 {} 已存在，跳过插入", cardBinDO.getCardBin());
                }
            }
            log.info("平台 {} 同步卡头数据完成，共处理 {} 条记录，成功插入 {} 条新记录",
                    platform.getDescription(), cardBinDOList.size(), successCount);

        } catch (Exception e) {
            log.error("同步平台 {} 卡头数据异常：{}", platform.getDescription(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 安全提取卡组织信息
     */
    private String extractCardScheme(Object extraObj) {
        if (extraObj instanceof Map<?, ?> extraMap) {
            Object cardScheme = extraMap.get("cardScheme");
            return cardScheme != null ? cardScheme.toString() : "";
        }
        return "";
    }
}