package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.time.*;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.hibernate.validator.constraints.Length;

import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改卡头管理参数
 *
 * <AUTHOR>
 * @since 2025/09/02 15:44
 */
@Data
@Schema(description = "创建或修改卡头管理参数")
public class CardBinReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 卡头号码
     */
    @Schema(description = "卡头号码")
    @NotBlank(message = "卡头号码不能为空")
    @Length(max = 64, message = "卡头号码长度不能超过 {max} 个字符")
    private String cardBin;

    /**
     * 卡头名称
     */
    @Schema(description = "卡头名称")
    @NotBlank(message = "卡头名称不能为空")
    @Length(max = 255, message = "卡头名称长度不能超过 {max} 个字符")
    private String name;

    /**
     * 所属平台
     */
    @Schema(description = "所属平台")
    @NotNull(message = "所属平台不能为空")
    private CardPlatformEnum platform;

    /**
     * 卡组织
     */
    @Schema(description = "卡组织")
    @Length(max = 255, message = "卡组织长度不能超过 {max} 个字符")
    private String cardScheme;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    @NotNull(message = "是否启用不能为空")
    private Boolean enable;
}