/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.controller.biz;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CachePenetrationProtect;
import com.alicp.jetcache.anno.CacheRefresh;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.continew.admin.biz.enums.CardPlatformEnum;
import top.continew.admin.biz.gmail.GzyGMailHelper;
import top.continew.admin.biz.gmail.ItlGMailHelper;
import top.continew.admin.biz.gmail.ValidateCodeResp;
import top.continew.admin.biz.model.entity.CardDO;
import top.continew.admin.biz.model.query.CardBinQuery;
import top.continew.admin.biz.model.query.CardQuery;
import top.continew.admin.biz.model.req.*;
import top.continew.admin.biz.model.resp.CardDetailResp;
import top.continew.admin.biz.model.resp.CardResp;
import top.continew.admin.biz.service.CardBinService;
import top.continew.admin.biz.service.CardService;
import top.continew.admin.common.base.BaseController;
import top.continew.admin.system.model.entity.UserDO;
import top.continew.admin.system.service.UserService;
import top.continew.starter.core.exception.BusinessException;
import top.continew.starter.extension.crud.annotation.CrudRequestMapping;
import top.continew.starter.extension.crud.enums.Api;
import top.continew.starter.extension.crud.model.resp.LabelValueResp;
import top.continew.starter.log.annotation.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * 卡片管理 API
 *
 * <AUTHOR>
 * @since 2024/12/28 10:43
 */
@Tag(name = "卡片管理 API")
@RestController
@CrudRequestMapping(value = "/biz/card", api = {Api.PAGE, Api.DETAIL, Api.UPDATE, Api.EXPORT})
@Slf4j
public class CardController extends BaseController<CardService, CardResp, CardDetailResp, CardQuery, CardReq> {
    @Resource
    private UserService userService;
    @Resource
    private GzyGMailHelper gzyGMailHelper;
    @Resource
    private ItlGMailHelper itlGMailHelper;
    @Resource
    private CardBinService cardBinService;
    @Log(ignore = true)
    @GetMapping("{cardNumber}/verifyCode")
    public String getVerifyCode(@PathVariable("cardNumber") String cardNumber) {
        return baseService.getCardVerifyCode(cardNumber);
    }

    @Log(ignore = true)
    @PutMapping("remark")
    public void updateRemark(@Validated @RequestBody CardPlatformUpdateReq req) {
        baseService.updateRemark(req);
    }

    @Log(ignore = true)
    @GetMapping("detail")
    public CardDO getDetail(String cardNumber) {
        return baseService.getByCardNumber(cardNumber, false);
    }

    @Operation(summary = "卡片充值", description = "卡片充值")
    @PostMapping("recharge")
    public void recharge(@Validated @RequestBody CardRechargeReq req) {
        baseService.recharge(req);
    }

    @Operation(summary = "开卡", description = "开卡")
    @PostMapping("open")
    public CardDO open(@Validated @RequestBody CardOpenReq req) {
        return baseService.open(req);
    }

    @Log(ignore = true)
    @PostMapping("getApplyCardResult")
    public CardDO getApplyCardResult(@Validated @RequestBody CardApplyCardResultReq req) {
        return baseService.getApplyCardResult(req.getPlatform(), req.getRequestId());
    }

    @Log(ignore = true)
    @GetMapping("/bins/{platform}")
    @CachePenetrationProtect
    @CacheRefresh(refresh = 7200)
    //@Cached(key = "#platform", name = CacheConstants.OPTION_KEY_PREFIX + "CARD_BIN:", cacheType = CacheType.BOTH, syncLocal = true)
    public List<LabelValueResp<String>> getCardBinList(@PathVariable CardPlatformEnum platform) {
        return cardBinService.getBinList(platform);
    }

    @Log(ignore = true)
    @GetMapping("checkUserOpenCardPerms")
    public List<Integer> checkUserOpenCardPerms() {
        Long userId = StpUtil.getLoginIdAsLong();
        UserDO currentUser = userService.getById(userId);

        List<Integer> platforms = new ArrayList<>();
//        platforms.add(CardPlatformEnum.CARD_VP.getValue());
        if (null != currentUser && StrUtil.isNotBlank(currentUser.getGzyCardHolderId())) {
            platforms.add(CardPlatformEnum.PHOTON_PAY.getValue());
        }

//        platforms.add(CardPlatformEnum.AMZ.getValue());
        platforms.add(CardPlatformEnum.INTERLACE.getValue());

        return platforms;
    }

    /**
     * 查询授权验证码数据
     *
     * @return
     */
    @Log(ignore = true)
    @GetMapping("{cardNumber}/emailVerifyCode")
    public ValidateCodeResp getEmailVerificationCode(@PathVariable String cardNumber) {
        CardDO card = baseService.getByCardNumberByCache(cardNumber);
        if (CardPlatformEnum.PHOTON_PAY.equals(card.getPlatform())) {
            String maskCardNumber = cardNumber.substring(0, 6) + "******" + cardNumber.substring(cardNumber.length() - 4);
            List<ValidateCodeResp> list = gzyGMailHelper.getVerificationCodesByCardLastFour(maskCardNumber);
            return CollUtil.isNotEmpty(list) ? list.get(0) : null;
        }else if(CardPlatformEnum.INTERLACE.equals(card.getPlatform())) {
            String maskCardNumber = cardNumber.substring(0, 8) + "xxxxxx" + cardNumber.substring(cardNumber.length() - 4);
            List<ValidateCodeResp> list = itlGMailHelper.getVerificationCodesByCardLastFour(maskCardNumber);
            return CollUtil.isNotEmpty(list) ? list.get(0) : null;

        }else {
            throw new BusinessException("卡片不支持获取邮件验证码");
        }

    }

    @Log(ignore = true)
    @PutMapping("{id}/active")
    public void active(@PathVariable("id") Long id) {
        this.baseService.active(id);
    }

    @Log(ignore = true)
    @PutMapping("{id}/inactive")
    public void inactive(@PathVariable("id") Long id) {
        this.baseService.inactive(id);
    }

    @Log(ignore = true)
    @PutMapping("{id}/syncBalance")
    public void syncBalance(@PathVariable("id") Long id) {
        this.baseService.syncCardBalance(id);
    }

    @GetMapping("/cardHeadList")
    @SaIgnore
    public List<String> cardHeadList() {
        return baseService.cardHeadList();
    }

    /**
     * 更新卡片使用状态
     *
     * @param id      卡片ID
     * @param hasUsed 是否已使用
     */
    @Operation(summary = "更新卡片使用状态", description = "更新卡片使用状态")
    @PutMapping("{id}/usedStatus")
    public void updateUsedStatus(@PathVariable("id") Long id, @RequestParam Boolean hasUsed) {
        baseService.updateUsedStatus(id, hasUsed);
    }

}